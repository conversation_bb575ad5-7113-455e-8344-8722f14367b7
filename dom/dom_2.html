<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <button>不知道</button>
    <div>赵金亮导管了么</div>
    <input type="text">
    <!--获取input输入框里面的内容 并将内容渲染到页面上-->
    <h1></h1>
    <script>
        let div = document.querySelector("div")
        let but = document.querySelector("button")
        
        but.addEventListener("click",()=>{div.innerHTML = "导了" 
        div.style.fontSize = "100px"
        div.style.color = "yellow" })
        
        //获取input
        let inp = document.querySelector("input")
        //获取h1标签
        let h = document.querySelector("h1")
        //change事件 （当表单里面的的值发生了变化 就会触发）
        //一般回调函数有一个默认的形参
        //e.target.value 获取input输入框里面的值
        inp.addEventListener("change",(e)=>{
            h.innerHTML = e.target.value
        })
    </script>
</body>
</html>