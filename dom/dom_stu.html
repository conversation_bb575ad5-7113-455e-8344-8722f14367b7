<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <div>0</div>
    <button>+1</button>
    <script>
        let but = document.querySelector("button")
        let div = document.querySelector("div")
        // 自增会自动转换类型 是js中隐式转换
        but.addEventListener("click",()=>{div.innerHTML++
            if(div.innerHTML>=10)
            {div.style.color = "yellow"}
            else
            {div.innerHTML.color = "black"}
            div.style.backgroundColor = "purple"
        })
        
    </script>
</body>
</html>