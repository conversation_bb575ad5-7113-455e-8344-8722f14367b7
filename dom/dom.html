<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>

    </style>
</head>
<body>
    <!--css中 修改div样式 1.style标签 2.选择器（class）-->
    <!--js中的选择器 叫做获取dom 获取标签对象-->
    <div id ="hello"></div>
    <div class="lei"></div>
    <div class="lei"></div>
    <div class="tongyong"></div>
    <div class="caozuo">操作</div>
    
    <p id ="myp"></p>
    <p></p>
    <span></span>
    <input type="text" value="man!">
    <button>oi</button>
    <script>
        //事件
        //事件三要素：事件源 事件类型 事件处理程序
        //我们希望点击这个按钮来做一些事情
        //1.获取按钮
        let but = document.querySelector("button")
        //2.点击按钮 为按钮增加点击事件
        //为按钮增加事件监听
        //事件源：谁触发了这个事件
        //事件类型：
        //事件处理程序：事件发生后会出现什么事情
        //but.addEventListener() 有两个参数 第一个参数：事件类型（click点击事件）第二个参数（回调函数）
        but.addEventListener("click",()=>{alert("manba out")})


        //可以通过js操作标签（不止css）
        //在js中把标签叫做dom


        //1.通过id获取标签
        //在js中操作html标签 必须通过顶端（document 即网页本身）来获取标签
        //document.getElementById() 通过id获取元素
        //将获取到的dom对象 存到变量b里面去 方便使用
        let a = document.getElementById("hello")
        console.log(document.getElementById("hello"));
        console.log(document.getElementById("ab"));
        //如果找到对应的元素 则这个方法将以对象（键值对）的形式返回该元素
        //如果没有该元素 则将返回一个null
        //id唯一 如果有两个相同的id 只能获取一个（默认第一个）


        //2.通过标签名获取
        //document.getElementsByTagName("p")
        //可以获取所有元素（id是唯一的，只能获取第一个 p标签不唯一）
        let c = document.getElementsByTagName("p")
        console.log(c);

        
        //3.通过类名找对应的html元素 类名也可以重复 全部获取
        //document.getElementsByClassName("lei")
        let d = document.getElementsByClassName("lei")
        console.log(d);

        //4.通用方式获取dom


        //document.querySelector
        let e = document.querySelector(".lei")
        console.log(e);
        //与css相同 类选择器以.作为前缀
        let f = document.querySelector("#myp")
        console.log(f);
        //id选择器以#作为前缀
        let g = document.querySelector("span")
        console.log(g);
        //标签选择器直接输入标签
        //document.querySelector只能获取一个标签

        //获取多个标签 document.querySelectorAll()
        h = document.querySelectorAll(".lei")
        console.log(h);

        //通过dom操作元素
        //1.获取标签 因为我们需要操作标签
        let i = document.querySelector(".caozuo")
        console.log(i);
        //改变文字颜色
        i.style.color = "purple"
        //改变文字大小（js不允许出现- 去掉- -后第一个字母改为大写 js中叫做驼峰命名法）
        i.style.fontSize = "50px"
        i.style.backgroundColor = "yellow"
       //js dom 用于用户与浏览器交互
       //js dom 写代码时没有提示


       console.dir(i)


       //通过console.dir()查看对象的结构
       //获取div里面的文字内容
       console.log(i.innerHTML);
       i.innerHTML = "what can i say"
       let j = document.querySelector("input")
       console.log(j);
       console.dir(j)
       //innerHTML 不适用于输入框获取值 只能用于输入框以外的元素 用value
       //console.log("input.innerHTML");
       console.log(j.value);

       
       
       
       
    
       
        
        
        

        

    </script>
</body>
</html>