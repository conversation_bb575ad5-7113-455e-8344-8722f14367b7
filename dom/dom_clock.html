<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <div>0</div>
    <button class="sto">stop</button>
    <script>
        //回调函数 间隔时间
        let clo = setInterval(()=>{
            div.innerHTML++
        },1000)
        //获取
        let div = document.querySelector("div")
        let but =document.querySelector("button")
        //增加点击事件
        but.addEventListener("click",()=>{
            //清除定时器
            clearInterval(clo)
        })
    </script>
</body>
</html>