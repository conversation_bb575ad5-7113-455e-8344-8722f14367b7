<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赵金亮在新疆</title>
    <!--style专门写css 点控制类名 font-size字体大小 font-weight字体粗细 font-family设置字体 分号间隔 color颜色左侧透明度右侧颜色-->
    <style>
        .a{font-size: 100px;
        font-weight: 200px;
    color: rgb(165, 218, 42);
    background-color: #d3c60d79;
    font-family: 宋体;
    }
        .b{font-size: 40px;
            font-family: '黑体';
        color: cornflowerblue;
    background-color: #1512cd;
    }
        .c{font-size: 50px;
        font-weight: 500px;
    color: rgba(0, 100, 0, 0.653);
background-color: rgba(199, 215, 24, 0.732);
    }
        .d{
            /*100%占满*/
            width: 700px;
            height: 700px;
        background-color: #288bb5;

        /*边框border 3个值 1.边框厚度 2.边框样式 3.边框颜色#*/
    border: 6px dotted #e4bd0e;

    /*内边距padding写两个值 上下和左右 上用top 下用bottom 左用left 右用right
    外边距margin
    border-radius设置盒子四个角更圆润 50%圆*/
    padding-top: 500px;
    margin: 50px 25%;

    font-size: 50px;
    color: #9c10ae;

    border-radius:50% ;

    /*左右居中text-align}*/
    /*背景颜色background-color 快捷方式bgc(打首字母)*/
    /*注释*/
    text-align:center;}
    </style>
</head>
<body>
    <img src="451725755553_.pic.jpg" alt="">
    <div class="a">赵金亮坐飞机去新疆</div>
    <div class="b">飞机在青岛胶东国际机场起飞</div>
    <!--快捷方式直接点a回车-->
    <div class="c">在新疆地窝堡机场降落</div>
    <div class="d">飞了5个小时</div>
    
</body>
</html>