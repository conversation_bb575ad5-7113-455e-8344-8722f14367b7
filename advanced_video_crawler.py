#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级视频爬虫 - 使用多种方法获取视频
"""

import requests
import os
import time
import re
import json
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
import cloudscraper
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

class AdvancedVideoCrawler:
    def __init__(self, video_url, download_dir="advanced_download"):
        self.video_url = video_url
        self.download_dir = download_dir
        self.captured_requests = []
        
        # 创建下载目录
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
    
    def setup_driver(self):
        """设置Chrome浏览器"""
        chrome_options = Options()

        # 基础设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')

        # 反检测设置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"Chrome驱动设置失败: {e}")
            return None
    
    def analyze_page_structure(self):
        """分析页面结构，寻找视频相关的API端点"""
        print("分析页面结构...")
        
        # 使用cloudscraper获取页面
        scraper = cloudscraper.create_scraper()
        try:
            response = scraper.get(self.video_url)
            html_content = response.text
        except Exception as e:
            print(f"获取页面失败: {e}")
            return []
        
        # 保存页面内容
        with open(os.path.join(self.download_dir, 'page_analysis.html'), 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 分析可能的API端点
        api_patterns = [
            r'["\']([^"\']*api[^"\']*video[^"\']*)["\']',
            r'["\']([^"\']*play[^"\']*)["\']',
            r'["\']([^"\']*stream[^"\']*)["\']',
            r'["\']([^"\']*\.mp4[^"\']*)["\']',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*explorePlay[^"\']*)["\']',
        ]
        
        potential_urls = set()
        for pattern in api_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 5:  # 过滤太短的匹配
                    potential_urls.add(match)
        
        print(f"发现 {len(potential_urls)} 个潜在的API端点:")
        for url in potential_urls:
            print(f"  - {url}")
        
        return list(potential_urls)
    
    def try_direct_api_calls(self):
        """尝试直接调用可能的API"""
        print("尝试直接API调用...")
        
        # 从URL中提取视频ID
        parsed_url = urlparse(self.video_url)
        video_id = None
        if 'id=' in parsed_url.query:
            video_id = parse_qs(parsed_url.query)['id'][0]
        
        if not video_id:
            print("无法提取视频ID")
            return []
        
        print(f"视频ID: {video_id}")
        
        # 常见的API端点模式
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
        api_endpoints = [
            f"/api/video/{video_id}",
            f"/api/play/{video_id}",
            f"/api/stream/{video_id}",
            f"/play/{video_id}",
            f"/stream/{video_id}",
            f"/video/{video_id}",
            f"/api/explorePlay?id={video_id}",
            f"/explorePlay/api?id={video_id}",
        ]
        
        scraper = cloudscraper.create_scraper()
        video_urls = []
        
        for endpoint in api_endpoints:
            full_url = base_url + endpoint
            try:
                print(f"尝试: {full_url}")
                response = scraper.get(full_url, timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    print(f"成功响应: {len(content)} 字符")
                    
                    # 保存响应内容
                    filename = f"api_response_{endpoint.replace('/', '_').replace('?', '_')}.json"
                    with open(os.path.join(self.download_dir, filename), 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    # 尝试解析JSON
                    try:
                        data = response.json()
                        print(f"JSON数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        
                        # 查找视频URL
                        def find_video_urls(obj, urls=None):
                            if urls is None:
                                urls = []
                            
                            if isinstance(obj, dict):
                                for key, value in obj.items():
                                    if isinstance(value, str) and any(ext in value.lower() for ext in ['.mp4', '.m3u8', '.flv']):
                                        urls.append(value)
                                    elif isinstance(value, (dict, list)):
                                        find_video_urls(value, urls)
                            elif isinstance(obj, list):
                                for item in obj:
                                    find_video_urls(item, urls)
                            
                            return urls
                        
                        found_urls = find_video_urls(data)
                        video_urls.extend(found_urls)
                        
                    except json.JSONDecodeError:
                        # 如果不是JSON，尝试查找URL模式
                        url_patterns = [
                            r'https?://[^"\s]+\.(?:mp4|m3u8|flv|avi|mov|webm)',
                            r'["\']([^"\']+\.(?:mp4|m3u8|flv))["\']'
                        ]
                        
                        for pattern in url_patterns:
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            video_urls.extend(matches)
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"请求失败 {full_url}: {e}")
                continue
        
        return list(set(video_urls))  # 去重
    
    def selenium_deep_analysis(self):
        """使用Selenium进行深度分析"""
        print("使用Selenium进行深度分析...")
        
        driver = self.setup_driver()
        if not driver:
            return []
        
        video_urls = []
        
        try:
            driver.get(self.video_url)
            
            # 等待页面加载
            time.sleep(10)
            
            # 尝试多种交互方式
            interactions = [
                "document.querySelector('video')?.play()",
                "document.querySelector('[class*=\"play\"]')?.click()",
                "document.querySelector('button')?.click()",
                "window.scrollTo(0, document.body.scrollHeight/2)",
            ]
            
            for interaction in interactions:
                try:
                    driver.execute_script(interaction)
                    time.sleep(3)
                except:
                    continue
            
            # 获取所有网络请求
            try:
                # 尝试获取浏览器的网络活动
                performance_logs = driver.execute_script("""
                    return window.performance.getEntriesByType('resource')
                        .filter(entry => entry.name.includes('video') || 
                                       entry.name.includes('mp4') || 
                                       entry.name.includes('m3u8') ||
                                       entry.name.includes('play') ||
                                       entry.name.includes('stream'))
                        .map(entry => entry.name);
                """)
                
                video_urls.extend(performance_logs)
                
            except Exception as e:
                print(f"获取性能日志失败: {e}")
            
            # 最终尝试获取所有可能的视频元素
            try:
                final_analysis = driver.execute_script("""
                    var results = [];
                    
                    // 查找video元素
                    document.querySelectorAll('video').forEach(v => {
                        if (v.src) results.push(v.src);
                        if (v.currentSrc) results.push(v.currentSrc);
                    });
                    
                    // 查找source元素
                    document.querySelectorAll('source').forEach(s => {
                        if (s.src) results.push(s.src);
                    });
                    
                    // 查找所有包含视频URL的属性
                    document.querySelectorAll('*').forEach(el => {
                        ['data-src', 'data-video', 'data-url'].forEach(attr => {
                            var value = el.getAttribute(attr);
                            if (value && (value.includes('.mp4') || value.includes('.m3u8'))) {
                                results.push(value);
                            }
                        });
                    });
                    
                    return results;
                """)
                
                video_urls.extend(final_analysis)
                
            except Exception as e:
                print(f"最终分析失败: {e}")
        
        finally:
            driver.quit()
        
        return list(set(video_urls))  # 去重
    
    def download_video(self, video_url, filename):
        """下载视频文件"""
        try:
            print(f"正在下载: {video_url}")
            
            scraper = cloudscraper.create_scraper()
            response = scraper.get(video_url, stream=True, timeout=60)
            response.raise_for_status()
            
            filepath = os.path.join(self.download_dir, filename)
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            print(f"\r下载进度: {percent:.1f}%", end='', flush=True)
            
            print(f"\n下载完成: {filename}")
            return True
            
        except Exception as e:
            print(f"\n下载失败: {e}")
            return False
    
    def crawl_comprehensive(self):
        """综合爬取方法"""
        print(f"开始综合爬取: {self.video_url}")
        
        all_video_urls = []
        
        # 方法1: 页面结构分析
        try:
            urls1 = self.analyze_page_structure()
            all_video_urls.extend(urls1)
        except Exception as e:
            print(f"页面分析失败: {e}")
        
        # 方法2: 直接API调用
        try:
            urls2 = self.try_direct_api_calls()
            all_video_urls.extend(urls2)
        except Exception as e:
            print(f"API调用失败: {e}")
        
        # 方法3: Selenium深度分析
        try:
            urls3 = self.selenium_deep_analysis()
            all_video_urls.extend(urls3)
        except Exception as e:
            print(f"Selenium分析失败: {e}")
        
        # 去重并过滤
        unique_urls = list(set(all_video_urls))
        valid_urls = [url for url in unique_urls if url and len(url) > 10]
        
        print(f"\n总共发现 {len(valid_urls)} 个视频链接:")
        for i, url in enumerate(valid_urls, 1):
            print(f"  {i}. {url}")
        
        # 保存所有发现的URL
        with open(os.path.join(self.download_dir, 'all_video_urls.json'), 'w', encoding='utf-8') as f:
            json.dump(valid_urls, f, ensure_ascii=False, indent=2)
        
        # 尝试下载
        success_count = 0
        for i, video_url in enumerate(valid_urls, 1):
            parsed_url = urlparse(video_url)
            ext = os.path.splitext(parsed_url.path)[1] or '.mp4'
            filename = f"video_{i:02d}{ext}"
            
            if self.download_video(video_url, filename):
                success_count += 1
        
        print(f"\n爬取完成！成功下载 {success_count}/{len(valid_urls)} 个视频")
        print(f"文件保存在: {os.path.abspath(self.download_dir)}")
        
        return success_count > 0

def main():
    video_url = "https://hgdv1juaa7ygm.xyz/explorePlay?id=1329031"
    
    crawler = AdvancedVideoCrawler(video_url)
    crawler.crawl_comprehensive()

if __name__ == "__main__":
    main()
