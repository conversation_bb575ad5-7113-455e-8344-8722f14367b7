<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度一下，你就知道</title>
    <style>
        *{
        /*清除浏览器边距*/
        padding: 0px;
        margin: 0px;
        /*网站头部*/
    }
    .head{
        width: 100%;
        height: 80px;
        display: flex;
        font-size: 15px;
        /*background-color: #d21515;*/
    }
    .left{
        flex: 1;
        display: flex;
        align-items: center;
        
    
    }
    .right{
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: end;
        

    }
    .left1{
        margin: 0px 10px;
        
    }
    .left1xinwen{
        margin-left: 15px;
    }
    
    .bot{
        margin: 0px 20px;
        padding: 5px 8px;
        background-color: #4E6EF2;
        color: #fff;
        border: none;
        border-radius:15%
    }
    /*网站内容*/
    .center{
        width: 800px;
        height: 500px;
        /*background-color: #74d30e;*/
        margin: auto;
        position: relative;
    }
    /*图片*/
    .pic{
        /*display: flex;
        justify-content: center;*/
        /*pic设置成图片的大小，直接margin：auto;*/
        width: 280px;
        height: 140px;
        margin:auto;
        /*background-color: #cce209;*/
    }
    /*输入框*/
    .sea{
        height: 60px;
        /*background-color: #e6e0e5;*/
        margin-top: 20px;
        display: flex;
        justify-content: center;
        /*子绝父相 移动相机图标*/
        position: relative;
    }
    .pic:hover{
        cursor: pointer;
    }
    /*按钮*/
    .sear{
        width: 550px;
        height: 44px;
        /*去除边框，高度一致 border: none;*/
        border: 2px solid #c4c7ce;
        border-radius: 10px 0 0 10px;
        /*左上 右上 右下 左下 顺时针设置*/
        
        
    }
    .but{
        width: 100px;
        height: 47.5px;
        background-color: #4E6EF2;
        color: #fff;
        border: none;
        border-radius:0 10px 10px 0;
        font-size: 17px;
    }
    .but:hover{
        background-color: #234cf0;
        /*手 鼠标移入按钮变手*/
        cursor: pointer;
    }
    .sear:hover{
        border-color: #a7aab5;
    }
    /*相机*/
    .xj{
        position: absolute;
        right: 190px;
        top: 14px;
    }
    .xj:hover{
        cursor: pointer;
    }
    /*麦克风*/
    .mai{
        position: absolute;
        right: 222px;
        top: 14px;
    }
    .mai:hover{
        cursor: pointer;
    }
    /*热搜*/
    .resou{
        width: 100%;
        height: 28px;
        display: flex;
        /*左右贴边对齐*/
        justify-content: space-between;
        /*background-color: #154ac5;*/
        margin-top: 28px;
        position: relative;
        
    }
    a{
        /*去下划线*/
        text-decoration-line: none;
        color: black;
        margin: 72px;

    }
    a:hover{
        cursor: pointer;
        color: #315efb;

    }
    ul{
        /*无序序列去黑点*/
        list-style: none;
        display: flex;
        margin-left: 97px;
        margin-right: 50px;
        flex-wrap: wrap;
        justify-content: space-between;
        position: relative;
    }
    li{
        width: 300px;
        height: 30px;
        
    }
    .jiantou{
        position: absolute;
        left: 137px;
        top: 4px;
    }
    .yuanquan{
        position: absolute;
        left: 660px;
        top: 4px;
    }
    .jiantou:hover{
        cursor: pointer;
    }
    .yuanquan{
        cursor: pointer;
    }
    li:hover{
        cursor: pointer;
        color: #315efb;
        text-decoration-line: underline;
    }
    /*热搜序号*/
    .xuhao{
        position: relative;
    }
    .top{
        position: absolute;
        left: 72px;
        top: 279px;
        width: 19px;
        height: 19px;
    }
    .one{
        position: absolute;
        left: 72px;
        top: 310px;
        width: 19px;
        height: 19px;
    }
    .two{
        position: absolute;
        left: 72px;
        top: 339px;
        width: 18px;
        height: 19px;
    }
    .three{
        position: absolute;
        left: 428px;
        top: 279px;
        width: 19px;
        height: 19px;
    }
    .four{
        position: absolute;
        left: 428px;
        top: 310px;
        width: 18px;
        height: 18px;
    }
    .five{
        position: absolute;
        left: 428px;
        top: 339px;
        width: 19px;
        height: 19px;
    }
    .hota{
        position: absolute;
        left: 228px;
        top: 304px;
        width: 25px;
        height: 26px;
    }
    .hotb{
        position: absolute;
        left: 310px;
        top: 334px;
        width: 25px;
        height: 26px;
    }
    .top:hover{
        cursor: pointer;
    }
    .one:hover{
        cursor: pointer;
    }
    .two:hover{
        cursor: pointer;
    }
    .three:hover{
        cursor: pointer;
    }
    .four:hover{
        cursor: pointer;
    }
    .five:hover{
        cursor: pointer;
    }
    .six:hover{
        cursor: pointer;
    }
    /*网站底部*/
    .bottom{
        width: 100%;
        height: 455px;
        display: flex;
        /*background-color: #9184dd;*/
        align-items: end;
        justify-content: center;
        font-size: smaller;
        color: #bbb;
        font-family: Arial,sans-serif;
        padding-bottom: 5px;
    }
   

    </style>
</head>
<body>
    <!--网站头部-->
    <div class="head">
      <div class="left">
        <div class="left1xinwen">新闻</div>
        <div class="left1">hao123</div>
        <div class="left1">地图</div>
        <div class="left1">贴吧</div>
        <div class="left1">视频</div>
        <div class="left1">图片</div>
        <div class="left1">网盘</div>
        <div class="left1">文库</div>
        <div class="left1">AI助手</div>
        <div class="left1">更多</div>
      </div>
      <div class="right">
        <div class="right1">设置</div>
        <button class="bot">登录</button>
      </div>
</div>
    <!--网站内容-->
    <div class="center">
        <!--再找一个爸爸让图片居中-->
        <div class="pic"> 
            <img  src="PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png" alt="" width="280px" height="140px"></div>
        <div class="sea">
            <input type="text" class="sear">
            <button class="but">百度一下</button>
            <img src="WeChat3de66f20cb813f3cc4f633cf6cd6d418.jpg" alt="" width="24px" height="20px" class="xj">
            <img src="WeChat7b5c96da7c752c25186d39e1404fb904.jpg" alt="" width="24px" height="22px" class="mai">
        </div>
        <div class="hot"></div>
    <!--热搜-->
       <div class="resou">
        <!--百度热搜-->
        <div><a href="https://top.baidu.com/board?platform=pc&sa=pcindex_entry" style="font-weight: 800;" style="font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;"
            >百度热搜</a></div>
            <!--箭头和圆圈-->
            <img src="WechatIMG99.jpg" alt="" width="14px" height="18px" class="jiantou">
            <img src="WechatIMG97.jpg" alt="" width="18px" height="18px" class="yuanquan">
        <!--换一换-->
        <div><a href="" style="color: #9195a3;">换一换</a></div>
        <!--无需列表里面有li-->
       </div>
       <ul style="font-weight: 600;">
        <li>清澈的爱 只为中国</li>
        <li>致敬，共和国奋斗者</li>
        <li>晚上去吃麻辣香锅</li>
        <li>女子高速上开40码被交警拦下</li>
        <li>日本首相石破茂发表就职演说</li>
        <li>留学生在意大利把马栗当板栗</li>
        </ul>
        <img src="WechatIMG100.jpg" alt="" class="top">
        <img src="WechatIMG101.jpg" alt="" class="one">
        <img src="WechatIMG102.jpg" alt="" class="two">
        <img src="WechatIMG103.jpg" alt="" class="three">
        <img src="WechatIMG104.jpg" alt="" class="four">
        <img src="WechatIMG105.jpg" alt="" class="five">
        <img src="WechatIMG106.jpg" alt="" class="hota">
        <img src="WechatIMG106.jpg" alt="" class="hotb">
</div>
    <div class="bottom">
        <div>关于百度</div>
        <div>About Baidu</div>
        <div>使用百度前必读</div>
        <div>帮助中心</div>
        <div>企业推广</div>
        <div>京公网安备11000002000001号</div>
        <div>京ICP证030173号</div>
        <div>互联网新闻信息服务许可证11220180008</div>
        <div>网络文化经营许可证：京网文〔2023〕1034-029号</div>
        <div>信息网络传播视听节目许可证 0110516</div>

</div>

    
    
    
</body>
</html>