# 视频爬虫使用说明

这个爬虫可以爬取受Cloudflare保护的视频网站内容，支持多种视频格式和反爬虫机制绕过。

## 功能特点

- 支持绕过Cloudflare保护
- 自动提取视频链接和缩略图
- 支持多种视频格式 (mp4, m3u8, flv, avi, mov, wmv, webm)
- 进度显示和断点续传
- 自动保存页面源码和视频信息

## 安装依赖

### 1. 安装Python包
```bash
pip install -r requirements.txt
```

### 2. 安装Chrome浏览器和ChromeDriver

#### Windows:
1. 下载并安装Chrome浏览器
2. 下载ChromeDriver: https://chromedriver.chromium.org/
3. 将ChromeDriver.exe放到PATH环境变量中

#### macOS:
```bash
# 使用Homebrew安装
brew install --cask google-chrome
brew install chromedriver
```

#### Linux (Ubuntu/Debian):
```bash
# 安装Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install google-chrome-stable

# 安装ChromeDriver
sudo apt install chromium-chromedriver
```

## 使用方法

### 基本使用
```bash
python video_crawler.py
```

### 自定义URL
修改 `video_crawler.py` 文件中的 `video_url` 变量：
```python
video_url = "你的视频链接"
```

### 自定义下载目录
```python
crawler = VideoCrawler(video_url, download_dir="自定义目录名")
```

## 输出文件

爬虫会在下载目录中创建以下文件：

- `video_01.mp4`, `video_02.mp4` ... - 下载的视频文件
- `thumbnail_01.jpg`, `thumbnail_02.jpg` ... - 缩略图文件
- `video_info.json` - 提取的视频信息
- `page_source.html` - 页面源码（用于调试）

## 注意事项

1. **合法使用**: 请确保您有权下载目标视频，遵守网站的使用条款和版权法律
2. **网络稳定**: 某些网站可能需要稳定的网络连接
3. **反爬虫机制**: 如果遇到新的反爬虫机制，可能需要更新代码
4. **下载速度**: 为避免被封IP，爬虫会控制请求频率

## 故障排除

### 1. ChromeDriver版本不匹配
确保ChromeDriver版本与Chrome浏览器版本兼容。

### 2. Cloudflare挑战失败
- 尝试更新cloudscraper包
- 检查网络连接
- 等待一段时间后重试

### 3. 视频链接提取失败
- 检查 `page_source.html` 文件
- 网站可能使用了新的加密或混淆技术
- 可能需要手动分析页面结构

### 4. 下载失败
- 检查网络连接
- 视频链接可能有时效性
- 尝试使用VPN

## 技术原理

1. **cloudscraper**: 绕过Cloudflare的JavaScript挑战
2. **Selenium**: 处理动态加载的内容
3. **BeautifulSoup**: 解析HTML并提取视频链接
4. **正则表达式**: 从JavaScript代码中提取视频URL

## 免责声明

此工具仅供学习和研究使用。使用者需要遵守相关法律法规和网站使用条款，不得用于商业用途或侵犯他人版权。作者不承担任何法律责任。
