<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <script>
        //条件判断
        let a = 5;
        let b = '5';
        //python中 if 条件 ：
        //js中 if (条件){要执行的代码}（和C语言一样）
        if (a === b) {
            console.log("条件成立");
        }
        else {
            console.log("条件不成立");
        }
        //多条件判断
        //python中 if...elif...else...
        //js中 if...else if...else...（和C语言相通）
        let c = 6;
        if (a > c) {
            console.log("a>c");
        }
        else if (a == c) {
            console.log("a=c");
        }
        else {
            console.log("a<c");
        }
        //逻辑运算符 js中没有and和or 用&& 和||
        //js中的for循环 
        //1.普通for循环：for(创建变量,循环条件,循环语句){}
        for (let i = 0; i < 5; i++) {
            console.log(i);
        }
        //2.根据可迭代对象循环 数组 字符串
        //python中 for...in...
        //js中 for()
        let arr = [1, 2, 3, 4, 5]
        //break跳出整个循环
        //continue跳出当前循环 进入下一个循环
        for (let o of arr) {
            if (o == 3) {
                break
            }
            console.log(o);
        }
        //3.对 对象循环 {}
        //和python相通
        let dic = {
            name: "zhangsan",
            age: 18,
            sex: "man"
        }
        for (let h in dic) {
            console.log(h, dic[h]);
        }
        //遍历出来的是键
        //它会默认从对象dic里面找键值对（而不是从循环里拿）
        //注意：如果要在循环里面 拿取对象里面的值 必须使用 对象[] （特殊情况）
        //while循环
        //while(条件){执行代码}
        while (a < 10) {
            console.log("无限循环");
            a++;
        }
        //无限循环会导致浏览器卡死
        //js中break和while在for和while循环的作用域是相同的
        //反引法 ``（英文输入下键盘左上角）可以通过``将变量插入到字符串中 类似python中的f格式化（f插值法）
        //`${}`
        console.log(`我今年${a}岁了`);
        //函数
        //1.普通函数 关键字 function 名字(){}
        function fun() {
            console.log("我是函数fun");
        }
        //调用函数
        fun()
        //2.箭头函数 
        //创建变量存入函数
        //箭头函数d
        let d = (x,y=10) => { 
            console.log(x+y);
        }
        d(1,5)//会覆盖
        //形参和实参一一对应
        //函数的嵌套
        let m = ()=>{
            console.log("我是函数m");
        }
        let n = ()=>{
            console.log("我是函数n");
            m()
        }
        n()
        //函数的执行顺序和调用顺序有关 谁先调用谁先执行
        //定时器（计时器）（闹钟）
        //两种定时器 setTimeout() setInterval()
        //setTimeout() 里面有两个参数 第一个参数是回调函数 第二个参数是时间
        //回调函数 不需要手动去调用 到了时间 自动调用函数
        //时间以毫秒ms为单位
        setTimeout(()=>{ console.log('两秒钟了');},2000)
        //只会执行一次
        //setInterval() 里面有两个参数 第一个参数是回调函数 第二个参数是时间
        //setInterval(()=>{console.log("三秒钟了");},3000)
        //每隔一段时间都会执行一次
        //点击按钮控制开始和暂停


    </script>
</body>

</html>