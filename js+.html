<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算数运算符</title>
</head>

<body>
    <script>
        //前提：数值类型
        let a = 10
        let b = 20
    console.log(a + b);
    console.log(a - b);
    console.log(a * b);
    console.log(a / b);
    //赋值运算符=
    let c = 10
    //累加+= 累减 -=
    c += 990
    console.log(c);
    c -= 500
    console.log(c);
    //比较运算符 == js中只判断值相不相等 不判断类型 true/false
    //===判断值和类型是否相等（py无）
    //不等于 ！=
    console.log(c == '500');
    console.log(c === '500');
    console.log(c != 1000);



    </script>
</body>

</html>