<!--全是标签 深蓝色是名字 浅蓝色是属性 黄色是内容 红色是报错-->
<!DOCTYPE html>
<!--html标签代表整个网页-->
<html lang="en">
<!-- head负责黑色头部部分·-->
<head>
    <!--前两行设置网站编码格式，第三行title设置网站标题-->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赵金亮简介</title>
</head>
<!-- body负责白色身体部分-->
<body>
    <h1>赵金亮简介</h1>
    <hr>
    <!--img图标插入标签，src是属性 引入照片 宽度width 高度hight 如果没有引入图片alt才会生效-->
    <img src="421725543142_.pic.jpg" alt="" width="400"height="400">
    <h2> <em>赵金亮</em> </h2>
    <p> <b>  <em>18岁</em> </b> </p>
    <hr>
    <p>齐鲁 <br> 工业 <br> 大学</p>
    <!--href跳转网站链接 中间写链接名-->
    <a href="https://www.qlu.edu.cn/"target="blank">齐鲁工业大学</a>
    <!-- h1-h6从大到小六种标题标签-->
    <!--有序列表 ol li  无序列表 ul li-->
    <ol>
    
    <li>青云学府</li>
    <li>电子信息</li>
    </ol>
    <!--input表单标签 type text输入框 password密码框 redio单选框 name相同多选一 checkbox name相同多选  file文件-->
   账号 <input type="text"><button>注册</button>
   <br>密码 <input type="password">
   <button>登录</button>
   <br><input type="radio"name="a">男
   <input type="radio"name="a">女
   <br><input type="checkbox"name="b">唱
   <input type="checkbox"name="b">跳
   <input type="checkbox"name="b">rap
   <input type="checkbox"name="b">篮球
  <hr><input type="file">
  <!--video视频标签 scoure设置上传的视频内容 type设置上传视频的格式 src视频地址 controls>播放 -->
  <video width="100" height="100" controls></video>视频<source src =""
  type=""></video>
</body>
</html>