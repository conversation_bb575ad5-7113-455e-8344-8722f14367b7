<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .demo{
            width: 200px;
            height: 200px;
            background-color: #8415af;
            /*margin：auto居中*/
            margin: auto;
            text-align:center;
            font-size: 50px;
            
        }
        .demo:hover{
            background-color: #1339d3;
            width: 500px;
            height: 500px;
            color: #51e806;
            color: #0ed239;border-radius:50% ;
        }
        .item{
            width: 500px;
            height: 500px;
            background-color: #b10e1e;
            margin: auto;
            /*给父亲加上相对定位 那儿子只能在父亲的范围内移动*/
            position: relative;
        }
        .item1{
            width: 200px;
            height: 200px;
            background-color: #ede909;
            margin: auto;
            /*谁要移动就给谁定位*/
            position: absolute;
            /*要给位置(右下）*/
            /*给·儿子绝对定位，给父亲相对定位：子绝父相能让儿子在父亲里面随意移动*/
            right: 10px;
            bottom:10px;

            margin: auto;
            text-align:center;
            font-size: 50px;
            color: #13b145;
        }

        
        /*.item1 :hover{
           
        }*/
    </style>
</head>
<!--兄弟关系（老大老二）（并列）-->
<div class="demo">赵金亮</div>
<div class="func"></div>
<!--父子关系,爷孙关系（嵌套）-->
<div class="item">
    <div class="item1">赵金亮</div>
</div>
<body>
    
</body>
</html>