#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版视频爬虫
专门针对特定网站优化
"""

import requests
import os
import time
import re
import json
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

class SimpleVideoCrawler:
    def __init__(self, video_url, download_dir="downloaded_video"):
        self.video_url = video_url
        self.download_dir = download_dir
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 创建下载目录
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
    
    def get_page_content(self, url, retries=3):
        """获取页面内容，支持重试"""
        for attempt in range(retries):
            try:
                print(f"正在访问页面 (尝试 {attempt + 1}/{retries}): {url}")
                
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                
                # 检查是否被重定向到错误页面
                if "error" in response.url.lower() or response.status_code != 200:
                    print(f"页面访问异常，状态码: {response.status_code}")
                    continue
                
                return response.text
                
            except requests.exceptions.RequestException as e:
                print(f"请求失败 (尝试 {attempt + 1}/{retries}): {e}")
                if attempt < retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                continue
        
        return None
    
    def extract_video_urls(self, html_content):
        """提取视频链接"""
        video_urls = []
        
        # 保存HTML内容用于调试
        with open(os.path.join(self.download_dir, 'page_source.html'), 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 1. 查找script标签中的视频链接
        script_patterns = [
            r'"(https?://[^"]+\.(?:mp4|m3u8|flv|avi|mov|wmv|webm)[^"]*)"',
            r"'(https?://[^']+\.(?:mp4|m3u8|flv|avi|mov|wmv|webm)[^']*)'",
            r'src["\s]*:["\s]*["\']([^"\']+\.(?:mp4|m3u8|flv|avi|mov|wmv|webm))[^"\']*["\']',
            r'url["\s]*:["\s]*["\']([^"\']+\.(?:mp4|m3u8|flv|avi|mov|wmv|webm))[^"\']*["\']',
            r'video["\s]*:["\s]*["\']([^"\']+)["\']',
            r'playUrl["\s]*:["\s]*["\']([^"\']+)["\']',
            r'videoUrl["\s]*:["\s]*["\']([^"\']+)["\']',
            r'file["\s]*:["\s]*["\']([^"\']+)["\']'
        ]
        
        for pattern in script_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if match and not match.startswith('data:'):
                    video_urls.append(match)
        
        # 2. 查找特定的API调用
        api_patterns = [
            r'/api/[^"\']*video[^"\']*',
            r'/play/[^"\']*',
            r'/stream/[^"\']*'
        ]
        
        for pattern in api_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                full_url = urljoin(self.video_url, match)
                video_urls.append(full_url)
        
        # 3. 查找base64编码的链接
        base64_pattern = r'data:video/[^;]+;base64,([A-Za-z0-9+/=]+)'
        base64_matches = re.findall(base64_pattern, html_content)
        for match in base64_matches:
            video_urls.append(f"data:video/mp4;base64,{match}")
        
        # 去重并过滤
        unique_urls = []
        seen = set()
        for url in video_urls:
            if url not in seen and len(url) > 10:
                seen.add(url)
                unique_urls.append(url)
        
        return unique_urls
    
    def extract_page_info(self, html_content):
        """提取页面信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        info = {
            'title': '',
            'description': '',
            'video_id': ''
        }
        
        # 提取标题
        title_elem = soup.find('title')
        if title_elem:
            info['title'] = title_elem.get_text().strip()
        
        # 从URL提取ID
        parsed_url = urlparse(self.video_url)
        if 'id=' in parsed_url.query:
            info['video_id'] = parsed_url.query.split('id=')[1].split('&')[0]
        
        return info
    
    def download_video(self, video_url, filename):
        """下载视频文件"""
        try:
            print(f"正在下载视频: {video_url}")
            
            # 处理相对URL
            if video_url.startswith('//'):
                video_url = 'https:' + video_url
            elif video_url.startswith('/'):
                video_url = urljoin(self.video_url, video_url)
            
            # 处理base64数据
            if video_url.startswith('data:'):
                print("检测到base64编码的视频数据")
                import base64
                header, data = video_url.split(',', 1)
                video_data = base64.b64decode(data)
                
                filepath = os.path.join(self.download_dir, filename)
                with open(filepath, 'wb') as f:
                    f.write(video_data)
                
                print(f"base64视频保存成功: {filename}")
                return True
            
            # 普通HTTP下载
            response = self.session.get(video_url, stream=True, timeout=60)
            response.raise_for_status()
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            
            filepath = os.path.join(self.download_dir, filename)
            
            with open(filepath, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            print(f"\r下载进度: {percent:.1f}% ({downloaded}/{total_size} bytes)", end='', flush=True)
            
            print(f"\n视频下载完成: {filename}")
            return True
            
        except Exception as e:
            print(f"\n视频下载失败: {e}")
            return False
    
    def crawl(self):
        """执行爬取"""
        print(f"开始爬取视频: {self.video_url}")
        
        # 获取页面内容
        html_content = self.get_page_content(self.video_url)
        if not html_content:
            print("无法获取页面内容")
            return False
        
        # 提取页面信息
        page_info = self.extract_page_info(html_content)
        print(f"页面标题: {page_info['title']}")
        print(f"视频ID: {page_info['video_id']}")
        
        # 提取视频链接
        video_urls = self.extract_video_urls(html_content)
        print(f"找到 {len(video_urls)} 个可能的视频链接:")
        
        for i, url in enumerate(video_urls, 1):
            print(f"  {i}. {url[:100]}{'...' if len(url) > 100 else ''}")
        
        # 保存视频信息
        info_data = {
            'page_info': page_info,
            'video_urls': video_urls,
            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open(os.path.join(self.download_dir, 'video_info.json'), 'w', encoding='utf-8') as f:
            json.dump(info_data, f, ensure_ascii=False, indent=2)
        
        # 尝试下载视频
        success_count = 0
        for i, video_url in enumerate(video_urls, 1):
            # 确定文件扩展名
            if video_url.startswith('data:'):
                ext = '.mp4'  # base64数据默认为mp4
            else:
                parsed_url = urlparse(video_url)
                ext = os.path.splitext(parsed_url.path)[1]
                if not ext:
                    ext = '.mp4'  # 默认扩展名
            
            filename = f"video_{page_info['video_id']}_{i:02d}{ext}"
            
            if self.download_video(video_url, filename):
                success_count += 1
            
            # 添加延迟
            time.sleep(1)
        
        print(f"\n爬取完成！")
        print(f"成功下载: {success_count}/{len(video_urls)} 个视频")
        print(f"文件保存在: {os.path.abspath(self.download_dir)}")
        
        return success_count > 0

def main():
    video_url = "https://hgdv1juaa7ygm.xyz/explorePlay?id=1329031"
    
    crawler = SimpleVideoCrawler(video_url)
    crawler.crawl()

if __name__ == "__main__":
    main()
