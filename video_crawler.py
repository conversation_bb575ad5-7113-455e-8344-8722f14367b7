#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频爬虫 - 支持绕过Cloudflare保护
爬取指定视频链接的内容
"""

import requests
import os
import time
import re
import json
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
import cloudscraper
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

class VideoCrawler:
    def __init__(self, video_url, download_dir="downloaded_videos"):
        self.video_url = video_url
        self.download_dir = download_dir
        
        # 使用cloudscraper绕过Cloudflare
        self.scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            }
        )
        
        # 创建下载目录
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
    
    def setup_driver(self):
        """设置Chrome浏览器驱动，配置绕过检测"""
        chrome_options = Options()

        # 基础设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')

        # 反检测设置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        try:
            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # 执行反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 启用网络日志
            driver.execute_cdp_cmd('Network.enable', {})

            return driver
        except Exception as e:
            print(f"Chrome驱动设置失败: {e}")
            print("请确保已安装Chrome浏览器")
            return None
    
    def get_page_with_cloudscraper(self, url):
        """使用cloudscraper获取页面内容"""
        try:
            print(f"使用cloudscraper访问: {url}")
            response = self.scraper.get(url, timeout=30)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"cloudscraper访问失败: {e}")
            return None
    
    def get_page_with_selenium(self, url):
        """使用Selenium获取页面内容"""
        driver = self.setup_driver()
        if not driver:
            return None

        try:
            print(f"使用Selenium访问: {url}")
            driver.get(url)

            # 等待页面加载
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 等待JavaScript应用加载
            print("等待JavaScript应用加载...")
            time.sleep(10)

            # 检查是否有Cloudflare挑战页面
            if "Checking your browser" in driver.page_source or "Just a moment" in driver.page_source:
                print("检测到Cloudflare挑战，等待...")
                WebDriverWait(driver, 30).until_not(
                    EC.text_to_be_present_in_element((By.TAG_NAME, "body"), "Checking your browser")
                )
                time.sleep(5)

            # 等待应用完全加载
            try:
                # 等待视频播放器或相关元素出现
                WebDriverWait(driver, 15).until(
                    lambda d: len(d.find_elements(By.TAG_NAME, "video")) > 0 or
                             len(d.find_elements(By.CSS_SELECTOR, "[class*='player']")) > 0 or
                             len(d.find_elements(By.CSS_SELECTOR, "[class*='video']")) > 0 or
                             "video" in d.page_source.lower()
                )
                print("检测到视频相关内容")
            except TimeoutException:
                print("未检测到视频元素，尝试触发视频加载...")

                # 尝试点击可能的播放按钮或视频区域
                try:
                    # 查找播放按钮
                    play_buttons = driver.find_elements(By.CSS_SELECTOR,
                        "[class*='play'], [class*='btn'], button, [role='button']")

                    for btn in play_buttons[:3]:  # 尝试前3个按钮
                        try:
                            if btn.is_displayed() and btn.is_enabled():
                                print(f"尝试点击按钮: {btn.get_attribute('class')}")
                                driver.execute_script("arguments[0].click();", btn)
                                time.sleep(3)
                                break
                        except:
                            continue

                    # 尝试滚动页面
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
                    time.sleep(2)

                except Exception as e:
                    print(f"触发交互失败: {e}")

            # 执行一些JavaScript来获取更多信息
            try:
                # 尝试获取全局变量中的视频信息
                video_data = driver.execute_script("""
                    var data = {};

                    // 查找常见的视频数据变量
                    if (window.videoData) data.videoData = window.videoData;
                    if (window.playerConfig) data.playerConfig = window.playerConfig;
                    if (window.videoUrl) data.videoUrl = window.videoUrl;
                    if (window.playUrl) data.playUrl = window.playUrl;
                    if (window.art) data.art = window.art;

                    // 查找ArtPlayer实例
                    if (window.artplayer) data.artplayer = window.artplayer;

                    // 查找video元素
                    var videos = document.querySelectorAll('video');
                    if (videos.length > 0) {
                        data.videoElements = Array.from(videos).map(v => ({
                            src: v.src,
                            poster: v.poster,
                            currentSrc: v.currentSrc,
                            duration: v.duration,
                            videoWidth: v.videoWidth,
                            videoHeight: v.videoHeight
                        }));
                    }

                    // 查找所有可能包含视频URL的script标签内容
                    var scripts = document.querySelectorAll('script');
                    var scriptContents = [];
                    for (var i = 0; i < scripts.length; i++) {
                        if (scripts[i].innerHTML &&
                            (scripts[i].innerHTML.includes('mp4') ||
                             scripts[i].innerHTML.includes('m3u8') ||
                             scripts[i].innerHTML.includes('video') ||
                             scripts[i].innerHTML.includes('play'))) {
                            scriptContents.push(scripts[i].innerHTML);
                        }
                    }
                    data.relevantScripts = scriptContents;

                    // 查找所有可能的API端点
                    var apiUrls = [];
                    var allText = document.documentElement.innerHTML;
                    var apiPatterns = [
                        /\\/api\\/[^"'\\s]+/g,
                        /\\/play\\/[^"'\\s]+/g,
                        /\\/stream\\/[^"'\\s]+/g,
                        /\\/video\\/[^"'\\s]+/g
                    ];

                    apiPatterns.forEach(pattern => {
                        var matches = allText.match(pattern);
                        if (matches) {
                            apiUrls = apiUrls.concat(matches);
                        }
                    });

                    data.apiUrls = [...new Set(apiUrls)];

                    return data;
                """)

                if video_data:
                    print(f"JavaScript获取的数据: {json.dumps(video_data, indent=2, ensure_ascii=False)}")

                    # 保存JavaScript获取的数据
                    with open(os.path.join(self.download_dir, 'js_data.json'), 'w', encoding='utf-8') as f:
                        json.dump(video_data, f, ensure_ascii=False, indent=2)

            except Exception as e:
                print(f"JavaScript执行失败: {e}")

            # 获取网络请求日志
            try:
                logs = driver.get_log('performance')
                network_requests = []

                for log in logs:
                    message = json.loads(log['message'])
                    if message['message']['method'] == 'Network.responseReceived':
                        response = message['message']['params']['response']
                        url = response['url']

                        # 检查是否是视频相关的请求
                        if any(ext in url.lower() for ext in ['.mp4', '.m3u8', '.flv', '.avi', '.mov', '.webm']) or \
                           any(keyword in url.lower() for keyword in ['video', 'play', 'stream', 'media']):
                            network_requests.append({
                                'url': url,
                                'mimeType': response.get('mimeType', ''),
                                'status': response.get('status', 0)
                            })

                if network_requests:
                    print(f"发现 {len(network_requests)} 个可能的视频请求:")
                    for req in network_requests:
                        print(f"  - {req['url']} ({req['mimeType']})")

                    # 保存网络请求信息
                    with open(os.path.join(self.download_dir, 'network_requests.json'), 'w', encoding='utf-8') as f:
                        json.dump(network_requests, f, ensure_ascii=False, indent=2)

            except Exception as e:
                print(f"获取网络日志失败: {e}")

            final_content = driver.page_source
            print(f"最终页面内容长度: {len(final_content)}")

            return final_content

        except TimeoutException:
            print("页面加载超时")
            return driver.page_source if driver else None
        except Exception as e:
            print(f"Selenium访问失败: {e}")
            return None
        finally:
            driver.quit()
    
    def extract_video_info(self, html_content):
        """从页面HTML中提取视频信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        video_info = {
            'title': '',
            'video_urls': [],
            'thumbnail_urls': [],
            'description': ''
        }
        
        # 提取标题
        title_selectors = [
            'title',
            'h1',
            '.video-title',
            '.title',
            '[class*="title"]'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem and title_elem.get_text().strip():
                video_info['title'] = title_elem.get_text().strip()
                break
        
        # 提取视频链接
        video_urls = set()
        
        # 1. video标签
        video_tags = soup.find_all('video')
        for video in video_tags:
            src = video.get('src')
            if src:
                video_urls.add(src)
            
            # source标签
            sources = video.find_all('source')
            for source in sources:
                src = source.get('src')
                if src:
                    video_urls.add(src)
        
        # 2. 在JavaScript中查找视频链接
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # 查找常见的视频URL模式
                video_patterns = [
                    r'"(https?://[^"]+\.(?:mp4|m3u8|flv|avi|mov|wmv|webm))"',
                    r"'(https?://[^']+\.(?:mp4|m3u8|flv|avi|mov|wmv|webm))'",
                    r'src["\s]*:["\s]*"([^"]+\.(?:mp4|m3u8|flv|avi|mov|wmv|webm))"',
                    r'url["\s]*:["\s]*"([^"]+\.(?:mp4|m3u8|flv|avi|mov|wmv|webm))"',
                    r'video["\s]*:["\s]*"([^"]+)"',
                    r'playUrl["\s]*:["\s]*"([^"]+)"'
                ]
                
                for pattern in video_patterns:
                    matches = re.findall(pattern, script.string, re.IGNORECASE)
                    for match in matches:
                        video_urls.add(match)
        
        # 3. 查找iframe中的视频
        iframes = soup.find_all('iframe')
        for iframe in iframes:
            src = iframe.get('src')
            if src and any(domain in src for domain in ['youtube', 'vimeo', 'bilibili', 'youku']):
                video_urls.add(src)
        
        video_info['video_urls'] = list(video_urls)
        
        # 提取缩略图
        thumbnail_urls = set()
        
        # poster属性
        for video in video_tags:
            poster = video.get('poster')
            if poster:
                thumbnail_urls.add(poster)
        
        # meta标签中的图片
        meta_images = soup.find_all('meta', {'property': re.compile(r'og:image|twitter:image')})
        for meta in meta_images:
            content = meta.get('content')
            if content:
                thumbnail_urls.add(content)
        
        video_info['thumbnail_urls'] = list(thumbnail_urls)
        
        return video_info
    
    def download_file(self, url, filename):
        """下载文件"""
        try:
            print(f"正在下载: {url}")
            
            # 处理相对URL
            if url.startswith('//'):
                url = 'https:' + url
            elif url.startswith('/'):
                url = urljoin(self.video_url, url)
            
            # 使用cloudscraper下载
            response = self.scraper.get(url, stream=True, timeout=60)
            response.raise_for_status()
            
            filepath = os.path.join(self.download_dir, filename)
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            print(f"\r下载进度: {percent:.1f}%", end='', flush=True)
            
            print(f"\n下载完成: {filename}")
            return True
            
        except Exception as e:
            print(f"\n下载失败 {url}: {e}")
            return False
    
    def crawl_video(self):
        """爬取视频"""
        print(f"开始爬取视频: {self.video_url}")

        # 直接使用Selenium，因为这个网站需要JavaScript渲染
        print("使用Selenium获取动态内容...")
        html_content = self.get_page_with_selenium(self.video_url)

        if not html_content:
            print("无法获取页面内容")
            return False
        
        # 保存页面HTML用于调试
        with open(os.path.join(self.download_dir, 'page_source.html'), 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 提取视频信息
        video_info = self.extract_video_info(html_content)
        
        # 保存视频信息
        with open(os.path.join(self.download_dir, 'video_info.json'), 'w', encoding='utf-8') as f:
            json.dump(video_info, f, ensure_ascii=False, indent=2)
        
        print(f"视频标题: {video_info['title']}")
        print(f"找到 {len(video_info['video_urls'])} 个视频链接")
        print(f"找到 {len(video_info['thumbnail_urls'])} 个缩略图链接")
        
        # 下载视频
        success_count = 0
        for i, video_url in enumerate(video_info['video_urls'], 1):
            # 获取文件扩展名
            parsed_url = urlparse(video_url)
            ext = os.path.splitext(parsed_url.path)[1] or '.mp4'
            filename = f"video_{i:02d}{ext}"
            
            if self.download_file(video_url, filename):
                success_count += 1
        
        # 下载缩略图
        for i, thumb_url in enumerate(video_info['thumbnail_urls'], 1):
            parsed_url = urlparse(thumb_url)
            ext = os.path.splitext(parsed_url.path)[1] or '.jpg'
            filename = f"thumbnail_{i:02d}{ext}"
            
            self.download_file(thumb_url, filename)
        
        print(f"\n爬取完成！成功下载 {success_count} 个视频文件")
        print(f"文件保存在: {os.path.abspath(self.download_dir)}")
        
        return success_count > 0

def main():
    video_url = "https://hgdv1juaa7ygm.xyz/explorePlay?id=1329031"
    
    crawler = VideoCrawler(video_url)
    crawler.crawl_video()

if __name__ == "__main__":
    main()
