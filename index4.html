<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
    *{
        /*清除浏览器边距*/
        padding: 0px;
        margin: 0px;
    }
    .item{
        /*flex:1 出现宽度自适应的情况 大哥占一半 二哥占一半（百分比分份）有flex布局才能分*/
        /*flex: 1;*/
        width: 200px;
        height: 200px;
        background-color: #d05353;

    }
    .item1{
        /*flex: 1*/
        width: 200px;
        height: 200px;
        background-color: #d053d0;
    }
    .func{/*爸爸flex布局，儿子左右排列*/
        display: flex;
        /*爸爸没有设置宽度 默认100%*/
        justify-content: center;
        align-items: center;
        background-color: aqua;
        height: 500px;
    }

    </style>
</head>
<body>
    <!--父子关系才能左右排列（用在爸爸身上），兄弟关系不行-->
    
    <div class="func">
        <div class="item">head</div>
        <div class="item1">body</div>
</div>
    
</body>
</html>