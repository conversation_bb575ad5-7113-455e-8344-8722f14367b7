<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>
        /*打印在控制台 快捷方式log生成 console.log();*/
        console.log('hello,javascript');
        /*将内容打印在网站中（弹窗）
        print查看代码的效果 和log一样*/
        alert('赵金亮')
       //注释双反斜杠
       //变量：存储数据的容器
       //let+变量名
       let a = 10
       console.log(a);
       //js数据类型
       //1.字符串类型string‘你好
       let b = '赵金亮'
       console.log(a,b);
       //2.数字类型number（包括整数和小数）
       let c = 1.2
       //4.布尔类型bool true 真 1 false 假 0
       let d = true
       let e 
       //4.undefind 未定义
       //网站提醒变量没有值（提示作用）
       console.log(e);
       //5。null 空 占位作用 没有内容
       //一般用在：你不知道变量要存什么内容的时候
       let f = null
       //typeof 判断变量类型
       console.log(typeof b);
       //小写深蓝色
    </script>
</body>
</html>