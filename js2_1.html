<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <script>
        //引用数据类型
        //1.数组 array [,]类似于python中的列表
        let arr = [1, 2, 3, 4, 5]
        //数组也可以通过索引来取值
        //正向索引 从左到右 从零开始
        console.log(arr[2]);
        //负向索引 从右到左 从-1开始 js不支持负向索引 python支持
        console.log(arr[-2]);
        console.log(arr.length);
        //如何向数组 添加或删除数据
        //向数组末尾 添加一个或多个元素 可重复 push()
        arr.push(6, 7, 8, 8)
        console.log(arr[5]);
        console.log(arr);
        //如何删除最后一个元素 pop()
        arr.pop()
        console.log(arr);
        //js中 规范是每一句都要以；结尾，但不强求
        //根据索引添加和删除元素 splice()
        //参数：1.开始的索引(插入后重排的索引) 2.删除元素的个数 不删写0 3.要插入的新元素（不想插入就不写）
        arr.splice(1,3,9);
        console.log(arr);
        //2.对象 类似python的字典 以键值对存储 多个键值对用，隔开（重复键会被覆盖掉）
        let dic = {
            name:"zhangsan",
            age:18,
            sex:"man"
        }
        //js中通过 对象.键 取值(python中通过 字典[键])
        console.log(dic.sex)
        




    </script>
</body>

</html>