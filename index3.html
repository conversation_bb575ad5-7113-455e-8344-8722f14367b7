<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
         *{/*清除浏览器边距*/
            padding: 0px;
            margin: 0px;
        }
        .top{
            width: 100%;
            height: 100px;
            background-color: #158f3e;
            /*固定定位*/
            position: fixed;
            /*想固定在上方，又不想影响下面*/
            top: 0px;
            text-align:center;
            line-height: 100px;
            
        }
        .center{
            width: 100%;
            height: 2000px;
            background-color: #2c66b6;
            margin-top: 100px;
            /*往上有100的距离就不会因为固定定位覆盖内容区域*/
            text-align:center;
            line-height: 2000px;
            
        }
    </style>
</head>
<body>
<div class="top">head</div>
<div class="center">body</div>
<div class="bottom"></div>
</body>
</html>